from django.db import models    


class TodoListStatusEnum(models.TextChoices):
    # 待办
    PENDING = "PENDING", "待办"
    # 进行中
    IN_PROGRESS = "IN_PROGRESS", "进行中"
    # 已完成
    COMPLETED = "COMPLETED", "已完成"
    # 已取消
    CANCELLED = "CANCELLED", "已取消"
    
    
# 待办事项类型
class TodoListTypeEnum(models.TextChoices):
    # 客户反馈
    FEEDBACK = "FEEDBACK", "客户反馈"
    # 客户投诉
    COMPLAINT = "COMPLAINT", "客户投诉"
    # 日常清洁
    DAILY_CLEANING = "DAILY_CLEANING", "日常清洁"
    # 客户关怀
    CUSTOMER_CARE = "CUSTOMER_CARE", "客户关怀"
    # 客户回访
    CUSTOMER_VISIT = "CUSTOMER_VISIT", "客户回访"
    # 其他
    OTHER = "OTHER", "其他"

    
    