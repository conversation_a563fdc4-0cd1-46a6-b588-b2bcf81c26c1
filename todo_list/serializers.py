from rest_framework import serializers
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from todo_list.models import TodoList
from user.models import Staff

# 待办事项列表
class TodoListSerializer(serializers.ModelSerializer):
    class Meta:
        model = TodoList
        fields = '__all__'
        
# 待办事项详情
class TodoListDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = TodoList
        fields = '__all__'
        
# 创建待办事项
class TodoAssignCreateSerializer(serializers.ModelSerializer):
    
    assigned_to = serializers.CharField(required=True)
    
    class Meta:
        model = TodoList
        fields = [
            'maternity_center',
            'assign',
            'assigned_to',
            'todo_type',
            'maternity_admission',
            'todo_content',
            'todo_remark',
            ]
    

    def validate_assigned_to(self,value):
        staff = Staff.get_staff_by_sid(sid=value,maternity_center=self.context['maternity_center'])
        if not staff:
            raise serializers.ValidationError("被指派人不存在")
        return staff
    
    def validate_maternity_admission(self,value):
        if not value:
            return None
        
        maternity_admission = MaternityAdmission.get_maternity_admission_by_aid(aid=value,maternity_center=self.context['maternity_center'])
        if not maternity_admission:
            raise serializers.ValidationError("入院单不存在")
        return maternity_admission
        

# 更新待办事项
class TodoAssignUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TodoList
        fields = [
            'todo_type'
            'todo_content',
            'todo_remark',
            ]
    



# 员工列表
class StaffSerializer(serializers.ModelSerializer):
    class Meta:
        model = Staff
        fields = ['sid','name']