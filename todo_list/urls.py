from django.urls import path

from permissions.views import RoleCreateView
from todo_list.views import TodoAssignCreateView, TodoAssignDeleteView, TodoAssignDetailView, TodoAssignListView, TodoAssignUpdateView, TodoDetailView, TodoListView


urlpatterns = [

    # 待办事项列表
    path('list/', TodoListView.as_view(), name='todo-list'),
    
    # 待办事项详情
    path('detail/<str:rid>/', TodoDetailView.as_view(), name='todo-detail'),
    
    
    # 待办事项列表
    path('assign/list/', TodoAssignListView.as_view(), name='todo-assign-list'),
    # 待办事项详情
    path('assign/detail/<str:rid>/', TodoAssignDetailView.as_view(), name='todo-assign-detail'),
    # 创建待办事项
    path('assign/create/', TodoAssignCreateView.as_view(), name='todo-assign-create'),
    # 更新待办事项
    path('assign/update/<str:rid>/', TodoAssignUpdateView.as_view(), name='todo-assign-update'),
    # 删除待办事项
    path('assign/delete/<str:rid>/', TodoAssignDeleteView.as_view(), name='todo-assign-delete'),
    
    # 员工列表
    path('staff/list/', StaffListView.as_view(), name='staff-list'),
]
