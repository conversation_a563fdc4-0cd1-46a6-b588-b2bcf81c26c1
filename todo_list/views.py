from rest_framework.views import APIView
from core.authorization import CareCenterAuthentication, HasStaffPermission, StaffWithSpecificPermissionOnly
from core.resp import make_response
from core.view import PaginationListBaseView
from permissions.enum import PermissionEnum
from todo_list.models import TodoList
from todo_list.serializers import StaffSerializer, TodoAssignCreateSerializer, TodoAssignUpdateSerializer, TodoListDetailSerializer, TodoListSerializer
from user.models import Staff


# 待办事项列表
class TodoListView(PaginationListBaseView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasStaffPermission]
    serializer_class = TodoListSerializer
    response_msg = "获取待办事项列表成功"
    error_response_msg = "获取待办事项列表失败"
    search_fields = []

    def get_queryset(self):
        return TodoList.get_list_by_assigned_to(self.request.user)
    
    
# 待办事项详情
class TodoDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasStaffPermission]
    
    def get(self, request, rid):
        
        todo = TodoList.get_assigned_by_rid(rid,request.user)
        
        if not todo:
            return make_response(code=-1,msg="待办事项不存在")
        
        
        serializer = TodoListDetailSerializer(todo)
        return make_response(code=0,msg='获取待办事项详情成功',data=serializer.data)



# 待办事项列表
class TodoAssignListView(PaginationListBaseView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT
    serializer_class = TodoListSerializer
    response_msg = "获取待办事项列表成功"
    
    def get_queryset(self):
        return TodoList.get_list_by_maternity_center(self.request.user.maternity_center)



# 待办事项详情
class TodoAssignDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT
    
    def get(self, request, rid):
        
        todo = TodoList.get_by_rid(rid,request.user.maternity_center)
        
        if not todo:
            return make_response(code=-1,msg="待办事项不存在")
        
        return make_response(code=0,msg='获取待办事项详情成功',data=TodoListDetailSerializer(todo).data)



# 创建待办事项
class TodoAssignCreateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT
    
    def post(self, request):
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        data['assign'] = request.user.id

        serializer = TodoAssignCreateSerializer(data=data,context={'maternity_center':request.user.maternity_center})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='创建待办事项成功',data=TodoListDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='创建待办事项失败',data=serializer.errors)
        

# 更新待办事项
class TodoAssignUpdateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT
    
    def put(self, request, rid):
        
        todo = TodoList.get_by_rid(rid,request.user.maternity_center)
        
        if not todo:
            return make_response(code=-1,msg="待办事项不存在")
        
        # if todo.assigned_to != request.user:
        #     return make_response(code=-1,msg="您没有权限更新此待办事项")
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = TodoAssignUpdateSerializer(todo,data=data,context={'maternity_center':request.user.maternity_center})
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='更新待办事项成功',data=TodoListDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='更新待办事项失败')
        

# 删除待办事项  
class TodoAssignDeleteView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT
    
    def delete(self, request, rid):
        todo = TodoList.get_by_rid(rid,request.user.maternity_center)
        
        if not todo:
            return make_response(code=-1,msg="待办事项不存在")
        
        todo.delete()
        
        return make_response(code=0,msg='删除待办事项成功')
    
    
    
    
    
# 员工列表
class StaffListView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT

    
    def get(self, request):
        return make_response(code=0,msg='获取员工列表成功',data=StaffSerializer(Staff.objects.all(),many=True).data)